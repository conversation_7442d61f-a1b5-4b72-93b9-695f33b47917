import { ClerkLoading, SignedIn, useUser } from "@clerk/clerk-react";
import { Loader } from "../Loader";
import { useEffect, useState } from "react";
import { fetchUserProfile } from "@/lib/utils";
import { useMediaSources } from "@/hooks/useMediaSources";
import MediaConfigurtion from "../../MeidiaConfigration";

const Widget = () => {
    const [profile, setProfile] = useState<{
        status: number;
        user: | ({
            subscription: {
                plan: 'PRO' | 'FREE'
            } | null
            studio: {
                id: string
                screen: string | null
                mic: string | null
                preset: 'HD' | 'SD'
                camera: string | null
                userId: string | null
            } | null
        } & {
            id: string
            email: string
            firstName: string | null
            lastName: string| null
            createdAt: string
            clerkId: string
        } )
    } | null>(null)


    const {user} = useUser()
    const {state , fetchMediaResources} = useMediaSources ()

    useEffect(() => {
        if(user && user.id) {
            fetchUserProfile(user.id)
                .then((p) => setProfile(p))
                .catch((error) => {
                    console.error('Failed to fetch user profile:', error)
                    // Create a fallback profile for development
                    const fallbackProfile = {
                        status: 200,
                        user: {
                            id: user.id,
                            email: user.emailAddresses?.[0]?.emailAddress || '<EMAIL>',
                            firstName: user.firstName,
                            lastName: user.lastName,
                            createdAt: new Date().toISOString(),
                            clerkId: user.id,
                            subscription: {
                                plan: 'FREE' as const
                            },
                            studio: {
                                id: 'default-studio',
                                screen: null,
                                mic: null,
                                preset: 'HD' as const,
                                camera: null,
                                userId: user.id
                            }
                        }
                    }
                    console.log('Using fallback profile:', fallbackProfile)
                    setProfile(fallbackProfile)
                })
        }
    }, [user])

    useEffect(() => {
        fetchMediaResources()
    }, [])

  return  (
  <div className="p-5">
    <ClerkLoading>
        <div className="h-full flex justify-center items-center">
            <Loader/>
        </div>
    </ClerkLoading>
    <SignedIn>
        {profile && profile.user ? (
             <MediaConfigurtion state={state} user={profile.user} />
            ) : (
         <div className="h-full w-full flex justify-center items-center">
                <div className="text-center">
                    <Loader color="#fff"/>
                </div>
            </div>
        ) }
    </SignedIn>
  </div>
  );
};

export default Widget;
