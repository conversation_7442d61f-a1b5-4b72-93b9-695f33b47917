import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import axios from "axios"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

const httpsClient = axios.create({
  baseURL: import.meta.env.VITE_HOST_URL,
  // headers: {
  //   "Content-Type": "application/json",
  // },
})

export const onCloseApp = () => window.ipcRenderer.send("closeApp")


export const fetchUserProfile = async (clerkId: string) => {
  try {
    console.log('Fetching user profile for:', clerkId)
    console.log('API URL:', import.meta.env.VITE_HOST_URL)

    const response = await httpsClient.get(`/auth/${clerkId}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    })

    console.log('User profile response:', response.data)
    return response.data
  } catch (error: any) {
    console.error('Error fetching user profile:', error)
    if (error.response) {
      console.error('Response data:', error.response.data)
      console.error('Response status:', error.response.status)
    } else if (error.request) {
      console.error('No response received:', error.request)
    } else {
      console.error('Error message:', error.message)
    }
    throw error
  }
}

export const getMediaSources = async () => {
  try {
    console.log("Getting media sources...");

    const displays = await window.ipcRenderer.invoke("getSources")
    console.log("Displays retrieved:", displays?.length || 0);

    const enumerateDevices = await window.navigator.mediaDevices.enumerateDevices()
    const audioInputs = enumerateDevices.filter(
      (device) => device.kind === "audioinput"
    )
    console.log("Audio inputs retrieved:", audioInputs?.length || 0);

    return {
      displays: displays || [],
      audio: audioInputs || [],
    }
  } catch (error) {
    console.error("Error getting media sources:", error);
    return {
      displays: [],
      audio: [],
    }
  }
}

export const updataStudioSettings = async (
  screen: string,
  audio: string,
  preset: 'HD' | 'SD',
  id: string
) => {
  try {
    console.log('=== UPDATING STUDIO SETTINGS ===')
    console.log('Input parameters:', { screen, audio, preset, id })
    console.log('API Base URL:', import.meta.env.VITE_HOST_URL)
    console.log('Full API URL:', `${import.meta.env.VITE_HOST_URL}/studio/${id}`)

    // Validate inputs
    if (!screen || !audio || !preset || !id) {
      throw new Error(`Missing required parameters: screen=${screen}, audio=${audio}, preset=${preset}, id=${id}`)
    }

    const requestData = {
      id,
      screen,
      audio,
      preset,
    }
    console.log('Request data:', requestData)

    const response = await httpsClient.post(`/studio/${id}`, requestData, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10 second timeout
    })

    console.log('✅ Studio settings updated successfully')
    console.log('Response status:', response.status)
    console.log('Response data:', response.data)
    return response.data
  } catch (error: any) {
    console.error('❌ ERROR UPDATING STUDIO SETTINGS')
    console.error('Error type:', error.constructor.name)
    console.error('Error message:', error.message)

    if (error.response) {
      console.error('HTTP Response Error:')
      console.error('- Status:', error.response.status)
      console.error('- Status Text:', error.response.statusText)
      console.error('- Response Data:', error.response.data)
      console.error('- Response Headers:', error.response.headers)
    } else if (error.request) {
      console.error('Network Error - No response received:')
      console.error('- Request:', error.request)
      console.error('- Possible causes: Server down, network issues, CORS, wrong URL')
    } else {
      console.error('Request Setup Error:', error.message)
    }

    // Check if it's a network connectivity issue
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      console.error('🔥 NETWORK CONNECTIVITY ISSUE: Cannot reach the server')
      console.error('- Check if the backend server is running')
      console.error('- Verify the VITE_HOST_URL is correct:', import.meta.env.VITE_HOST_URL)
    }

    throw error
  }
}

export const hidePluginWindow = (state: boolean) => {
  window.ipcRenderer.send("hide-plugin" , {state})
}

export const checkServerHealth = async () => {
  try {
    console.log('🔍 Checking server health...')
    console.log('Server URL:', import.meta.env.VITE_HOST_URL)

    const response = await httpsClient.get('/health', {
      timeout: 5000, // 5 second timeout
    })

    console.log('✅ Server is healthy:', response.data)
    return { healthy: true, data: response.data }
  } catch (error: any) {
    console.error('❌ Server health check failed:', error.message)

    if (error.code === 'ECONNREFUSED') {
      console.error('🔥 Server is not running or not reachable')
    } else if (error.code === 'ENOTFOUND') {
      console.error('🔥 Server hostname not found')
    } else if (error.response?.status === 404) {
      console.warn('⚠️ Health endpoint not found, but server is responding')
      return { healthy: true, data: 'Server responding but no health endpoint' }
    }

    return { healthy: false, error: error.message }
  }
}

export const videoRecordingTime = (ms: number) => {
  const second = Math.floor((ms / 1000) % 60).toString().padStart(2, '0')
  const minute = Math.floor((ms / (1000 * 60)) % 60).toString().padStart(2, '0')
  const hour = Math.floor((ms / (1000 * 60 * 60)) % 24).toString().padStart(2, '0')

  return {length: `${hour}:${minute}:${second}`, minute}
}