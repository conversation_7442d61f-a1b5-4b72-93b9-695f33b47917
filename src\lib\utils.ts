import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import axios from "axios"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

const httpsClient = axios.create({
  baseURL: import.meta.env.VITE_HOST_URL,
  // headers: {
  //   "Content-Type": "application/json",
  // },
})

export const onCloseApp = () => window.ipcRenderer.send("closeApp")


export const fetchUserProfile = async (clerkId: string) => {
  try {
    console.log('Fetching user profile for:', clerkId)
    console.log('API URL:', import.meta.env.VITE_HOST_URL)

    const response = await httpsClient.get(`/auth/${clerkId}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    })

    console.log('User profile response:', response.data)
    return response.data
  } catch (error: any) {
    console.error('Error fetching user profile:', error)
    if (error.response) {
      console.error('Response data:', error.response.data)
      console.error('Response status:', error.response.status)
    } else if (error.request) {
      console.error('No response received:', error.request)
    } else {
      console.error('Error message:', error.message)
    }
    throw error
  }
}

export const getMediaSources = async () => {
  try {
    console.log("Getting media sources...");

    const displays = await window.ipcRenderer.invoke("getSources")
    console.log("Displays retrieved:", displays?.length || 0);

    const enumerateDevices = await window.navigator.mediaDevices.enumerateDevices()
    const audioInputs = enumerateDevices.filter(
      (device) => device.kind === "audioinput"
    )
    console.log("Audio inputs retrieved:", audioInputs?.length || 0);

    return {
      displays: displays || [],
      audio: audioInputs || [],
    }
  } catch (error) {
    console.error("Error getting media sources:", error);
    return {
      displays: [],
      audio: [],
    }
  }
}

export const updataStudioSettings = async (
  screen: string,
  audio: string,
  preset: 'HD' | 'SD',
  id: string
) => {
  try {
    console.log('Updating studio settings:', { screen, audio, preset, id })
    console.log('API URL:', import.meta.env.VITE_HOST_URL)

    const response = await httpsClient.post(`/studio/${id}`, {
      id,
      screen,
      audio,
      preset,
    }, {
      headers: {
        'Content-Type': 'application/json',
      }
    })

    console.log('Studio settings response:', response.data)
    return response.data
  } catch (error: any) {
    console.error('Error updating studio settings:', error)
    if (error.response) {
      console.error('Response data:', error.response.data)
      console.error('Response status:', error.response.status)
    } else if (error.request) {
      console.error('No response received:', error.request)
    } else {
      console.error('Error message:', error.message)
    }
    throw error
  }
}

export const hidePluginWindow = (state: boolean) => {
  window.ipcRenderer.send("hide-plugin" , {state})
}

export const videoRecordingTime = (ms: number) => {
  const second = Math.floor((ms / 1000) % 60).toString().padStart(2, '0')
  const minute = Math.floor((ms / (1000 * 60)) % 60).toString().padStart(2, '0')
  const hour = Math.floor((ms / (1000 * 60 * 60)) % 24).toString().padStart(2, '0')

  return {length: `${hour}:${minute}:${second}`, minute}
}