import { But<PERSON> } from "@/components/ui/button";
import { SignedOut, SignInButton } from "@clerk/clerk-react";



const AuthButton = () => {
  return (
    <SignedOut>
        <div className="flex gap-x-3 h-screen justify-center items-center">
            <SignInButton>
                <Button className="px-10 rounded-full hover:bg-gray-200" 
                variant="outline">
                    Sign In
                </Button>
            </SignInButton>
            <SignInButton>
                <Button className="px-10 rounded-full hover:bg-gray-200" 
                variant="outline">
                    Sign Up
                </Button>
            </SignInButton>
        </div>
    </SignedOut>
  )
};

export default AuthButton;
