import { SourceDeviceStateProps } from '@/hooks/useMediaSources'
import { useStudioSettings } from '@/hooks/useStudioSettings'
import { Loader } from '../AuthButton/Loader'
import { Headphones, Monitor, Settings2 } from 'lucide-react'
// import { de } from 'date-fns/locale'
// import React from 'react'

type Props = {
    state: SourceDeviceStateProps
    user: | ({
        subscription: {
            plan: 'PRO' | 'FREE'
        } | null
        studio: {
            id: string
            screen: string | null
            mic: string | null
            preset: 'HD' | 'SD'
            camera: string | null
            userId: string | null
        } | null
    } & {
        id: string
        email: string
        firstName: string | null
        lastName: string| null
        createdAt: string
        clerkId: string
    }) | null
}

const MediaConfigurtion = ({state , user}: Props) => {

    const activeScreen = state.displays?.find((screen) => screen.id === user?.studio?.screen)


    const activeAudio = state.audioInputs?.find((device) => device.deviceId === user?.studio?.mic   )


    const {isPending, register, onPreset} = useStudioSettings(   
        user!.id,
        user?.studio?.screen || state.displays?.[0]?.id,
        user?.studio?.mic || state.audioInputs?.[0]?.deviceId,
        user?.studio?.preset,
        user?.subscription?.plan,
    )
    console.log(state);
    
  return (
    <form className='flex relative w-full flex-col gap-y-3'>
        {isPending && (
            <div className='absolute inset-0 z-50 rounded-xl bg-black/60 backdrop-blur-sm flex justify-center items-center'>
                <Loader/>
            </div>
        )}

        {/* Screen Selection */}
        <div className='space-y-2'>
            <label className='flex items-center gap-2 text-xs font-medium text-gray-300'>
                <Monitor size={16} className='text-blue-400'/>
                Screen
            </label>
            <select {...register('screen')}
                className='w-full px-3 py-2 rounded-lg bg-white/5 border border-white/20 text-white text-sm outline-none focus:border-blue-400 focus:ring-1 focus:ring-blue-400 transition-colors non-draggable'
            >
                {state.displays?.map((display, key) => (
                    <option
                        selected={activeScreen && activeScreen.id === display.id}
                        value={display.id}
                        className='bg-gray-800 text-white'
                        key={key}
                    >
                        {display.name}
                    </option>
                ))}
            </select>
        </div>

        {/* Audio Selection */}
        <div className='space-y-2'>
            <label className='flex items-center gap-2 text-xs font-medium text-gray-300'>
                <Headphones size={16} className='text-green-400'/>
                Audio
            </label>
            <select {...register('audio')}
                className='w-full px-3 py-2 rounded-lg bg-white/5 border border-white/20 text-white text-sm outline-none focus:border-green-400 focus:ring-1 focus:ring-green-400 transition-colors non-draggable'
            >
                {state.audioInputs?.map((device, key) => (
                    <option
                        selected={activeAudio && activeAudio.deviceId === device.deviceId}
                        value={device.deviceId}
                        className='bg-gray-800 text-white'
                        key={key}
                    >
                        {device.label || 'Unknown Device'}
                    </option>
                ))}
            </select>
        </div>

        {/* Quality Selection */}
        <div className='space-y-2'>
            <label className='flex items-center gap-2 text-xs font-medium text-gray-300'>
                <Settings2 size={16} className='text-purple-400'/>
                Quality
            </label>
            <select {...register('preset')}
                className='w-full px-3 py-2 rounded-lg bg-white/5 border border-white/20 text-white text-sm outline-none focus:border-purple-400 focus:ring-1 focus:ring-purple-400 transition-colors non-draggable'
            >
                <option
                    disabled={user?.subscription?.plan === 'FREE'}
                    selected={onPreset === 'HD' || user?.studio?.preset === 'HD'}
                    value={'HD'}
                    className='bg-gray-800 text-white'
                >
                    1080p HD{user?.subscription?.plan === 'FREE' ? ' (Pro Only)' : ''}
                </option>
                <option
                    value={'SD'}
                    selected={onPreset === 'SD' || user?.studio?.preset === 'SD'}
                    className='bg-gray-800 text-white'
                >
                    720p SD
                </option>
            </select>
        </div>
    </form>
  )
}
export default MediaConfigurtion