import { useZodForm } from "./useZodForm"
import { useEffect, useState } from "react"
import { useMutation } from "@tanstack/react-query"
import { updataStudioSettings, checkServerHealth } from "@/lib/utils"
import { toast } from "sonner"
import { updataStudioSettingsSchema } from "@/schema/studio-settings.schema"

export const useStudioSettings = (
    id: string,
    screen?: string | null,
    audio?: string | null,
    preset?: 'HD' | 'SD',
    plan?: 'PRO' | 'FREE',
    userId?: string | null
) => {
    const [onPreset, setOnPreset] = useState<'HD' | 'SD' | undefined>()

    const {register, watch } = useZodForm(updataStudioSettingsSchema, {
        screen: screen || '',
        audio: audio || '',
        preset: preset || 'HD',
    })

    const {mutate, isPending} = useMutation({
        mutationKey: ['update-studio'],
        mutationFn: (data: {
            screen: string
            audio: string
            preset: 'HD' | 'SD'
            id: string
        }) => {
            console.log('🔄 Mutation triggered with data:', data)
            return updataStudioSettings(data.screen, data.audio, data.preset, data.id)
        },
        onSuccess: (data)=> {
            console.log('✅ Mutation successful:', data)
            return toast(data?.status === 200 ? 'success' : 'error' ,{
                description: data?.message || 'Settings updated successfully',
            } )
        },
        onError: (error: any) => {
            console.error('❌ Mutation failed:', error)

            let errorMessage = 'Failed to update settings. Please try again.'

            if (error.response) {
                if (error.response.status === 404) {
                    errorMessage = 'Studio not found. Please refresh and try again.'
                } else if (error.response.status === 400) {
                    errorMessage = 'Invalid settings data. Please check your selections.'
                } else if (error.response.status === 500) {
                    errorMessage = 'Server error. Please try again later.'
                } else {
                    errorMessage = `Server error (${error.response.status}). Please try again.`
                }
            } else if (error.request) {
                errorMessage = 'Cannot connect to server. Please check your internet connection.'
            } else if (error.message.includes('Missing required parameters')) {
                errorMessage = 'Invalid settings. Please select both screen and audio sources.'
            }

            return toast('error', {
                description: errorMessage,
            })
        }
    })

    useEffect(() => {
        if(screen && audio){
            window.ipcRenderer.send('media-sources', {
                screen,
                audio,
                preset,
                plan,
                id
            })
        }
    }, [screen, audio])

    useEffect(() => {
        const subscribe = watch((values) => {
            console.log('📝 Form values changed:', values)

            // Validate required fields
            if (!values.screen || !values.audio || !values.preset || !id) {
                console.warn('⚠️ Skipping mutation - missing required fields:', {
                    screen: values.screen,
                    audio: values.audio,
                    preset: values.preset,
                    id
                })
                return
            }

            setOnPreset(values.preset)

            const mutationData = {
                screen: values.screen,
                audio: values.audio,
                preset: values.preset,
                id,
            }

            console.log('🚀 Calling mutation with:', mutationData)
            mutate(mutationData)

            // Send to IPC for studio tray
            const ipcData = {
                screen: values.screen,
                audio: values.audio,
                preset: values.preset,
                plan,
                id
            }
            console.log('📡 Sending to IPC:', ipcData)
            window.ipcRenderer.send('media-sources', ipcData)
        })

        return () => {
            subscribe.unsubscribe()
        }
    }, [watch, id, plan])

    const debugConnection = async () => {
        console.log('🔧 DEBUG: Testing server connection...')
        const health = await checkServerHealth()
        console.log('Health check result:', health)

        if (health.healthy) {
            toast('success', {
                description: 'Server connection is working!'
            })
        } else {
            toast('error', {
                description: `Server connection failed: ${health.error}`
            })
        }
    }

    return {
        register,
        isPending,
        onPreset,
        debugConnection
    }
        
}