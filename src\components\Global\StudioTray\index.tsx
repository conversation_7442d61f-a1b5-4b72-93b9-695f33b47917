import { StopRecording, StartRecording } from '@/lib/recorder'
import { cn, videoRecordingTime } from '@/lib/utils'
import { set } from 'date-fns'
import { Cast, Pause, Square } from 'lucide-react'
import React, { useEffect, useRef, useState } from 'react'


const StudioTray = () => {
    const initialTime = new Date()
    
    const intervalRef = useRef<NodeJS.Timeout | null>(null)
    const [preview, setPreview] = useState(false)
    const [onTimer, setOnTimer] = useState('00:00:00')
    const [count, setCount] = useState(0)
    const [recording, setRecording] = useState(false)
    const [onSources, setOnSources ] = useState< |
    {
        screen: string
        id: string
        audio: string
        preset: 'HD' | 'SD'
        plan: 'PRO' | 'FREE'
    } | undefined
    >(undefined)

    const clearTime = () => {
        setOnTimer('00:00:00')
        setCount(0)
    }


    window.ipcRenderer.on('profile-recieved' , (event , payload) => {
        console.log(event);
        setOnSources(payload)
    })

   const videoElement = useRef<HTMLVideoElement | null>(null)


   useEffect(() => {
       if(!recording) {
           clearTime()
           return
       }

     
       const recordTimeInterval = setInterval(() => {
           const time = count + (new Date().getTime() - initialTime.getTime())
           setCount(time)
           const recordingTime = videoRecordingTime(time)
           setOnTimer(recordingTime.length)

           if(onSources?.plan === 'FREE' && recordingTime.minute === '05'){
               setRecording(false)
               clearTime()
               StopRecording()
           }
           setOnTimer(recordingTime.length)
           if(time <= 0){
            setOnTimer('00:00:00')
            clearInterval(recordTimeInterval)
           }
       }, 1)
       return () => {
           clearInterval(recordTimeInterval)
       }
   }, [recording, count, onSources?.plan])

// Show compact recording controls
return !onSources ? (
    <div className='h-full flex items-center justify-center p-4 draggable'>
        <div className='bg-black/80 backdrop-blur-md rounded-xl border border-white/20 p-4 text-center'>
            <div className='animate-pulse'>
                <div className='w-4 h-4 bg-blue-400 rounded-full mx-auto mb-2'></div>
                <p className='text-xs text-gray-400'>Waiting for settings...</p>
            </div>
        </div>
    </div>
) : (
    <div className='h-full flex items-center justify-center p-2 draggable'>
        {preview && (
            <video
                autoPlay
                ref={videoElement}
                className='absolute top-0 left-0 w-full h-full object-cover rounded-xl'
            />
        )}

        <div className='bg-black/80 backdrop-blur-md rounded-xl border border-white/20 p-3 flex items-center gap-3 min-w-[250px]'>
            {/* Record Button */}
            <div
                {...(onSources && {
                    onClick: async () => {
                        if (!recording) {
                            try {
                                setRecording(true)
                                await StartRecording(onSources)
                            } catch (error) {
                                console.error('Failed to start recording:', error)
                                setRecording(false)
                            }
                        }
                    },
                })}
                className={cn(
                    'non-draggable rounded-full relative hover:opacity-80 cursor-pointer transition-all duration-200 flex items-center justify-center',
                    recording ? 'bg-red-500 w-6 h-6 animate-pulse' : 'bg-red-400 w-8 h-8 hover:bg-red-500'
                )}
            >
                {recording && (
                    <div className='absolute -top-8 left-1/2 transform -translate-x-1/2'>
                        <span className='text-xs text-white bg-black/60 px-2 py-1 rounded whitespace-nowrap'>
                            {onTimer}
                        </span>
                    </div>
                )}
            </div>

            {/* Stop/Pause Button */}
            {!recording ? (
                <Pause
                    className='non-draggable opacity-50 text-white'
                    size={20}
                    fill='currentColor'
                    stroke='none'
                />
            ) : (
                <Square
                    size={20}
                    className='non-draggable cursor-pointer hover:scale-110 transform transition duration-150 text-white hover:text-red-400'
                    fill='currentColor'
                    onClick={() => {
                        StopRecording()
                        setRecording(false)
                        clearTime()
                    }}
                    stroke='currentColor'
                />
            )}

            {/* Preview Toggle */}
            <Cast
                onClick={() => setPreview((prev) => !prev)}
                size={20}
                className={cn(
                    'non-draggable cursor-pointer transition-colors',
                    preview ? 'text-blue-400' : 'text-white hover:text-blue-400'
                )}
                fill='currentColor'
                stroke='currentColor'
            />

            {/* Status Indicator */}
            <div className='flex items-center gap-1 ml-auto'>
                <div className={cn(
                    'w-2 h-2 rounded-full',
                    recording ? 'bg-red-400 animate-pulse' : 'bg-green-400'
                )}></div>
                <span className='text-xs text-gray-400'>
                    {recording ? 'REC' : 'READY'}
                </span>
            </div>
        </div>
    </div>
)
  
}

export default StudioTray