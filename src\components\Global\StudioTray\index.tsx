import { StopRecording, StartRecording } from '@/lib/recorder'
import { cn, videoRecordingTime } from '@/lib/utils'
import { set } from 'date-fns'
import { Cast, Pause, Square } from 'lucide-react'
import React, { useEffect, useRef, useState } from 'react'


const StudioTray = () => {
    const initialTime = new Date()
    
    const intervalRef = useRef<NodeJS.Timeout | null>(null)
    const [preview, setPreview] = useState(false)
    const [onTimer, setOnTimer] = useState('00:00:00')
    const [count, setCount] = useState(0)
    const [recording, setRecording] = useState(false)
    const [onSources, setOnSources ] = useState< |
    {
        screen: string
        id: string
        audio: string
        preset: 'HD' | 'SD'
        plan: 'PRO' | 'FREE'
    } | undefined
    >(undefined)

    const clearTime = () => {
        setOnTimer('00:00:00')
        setCount(0)
    }


    window.ipcRenderer.on('profile-recieved' , (event , payload) => {
        console.log(event);
        setOnSources(payload)
    })

   const videoElement = useRef<HTMLVideoElement | null>(null)

   const onStopRecording = () => {
       setRecording(false)
       clearTime()
   }

   useEffect(() => {
       if(!recording) {
           clearTime()
           return
       }

     
       const recordTimeInterval = setInterval(() => {
           const time = count + (new Date().getTime() - initialTime.getTime())
           setCount(time)
           const recordingTime = videoRecordingTime(time)
           setOnTimer(recordingTime.length)

           if(onSources?.plan === 'FREE' && recordingTime.minute === '05'){
               setRecording(false)
               clearTime()
               StopRecording()
           }
           setOnTimer(recordingTime.length)
           if(time <= 0){
            setOnTimer('00:00:00')
            clearInterval(recordTimeInterval)
           }
       }, 1)
       return () => {
           clearInterval(recordTimeInterval)
       }
   }, [recording, count, onSources?.plan])

return !onSources ? (<></>) :
(
    <div className='flex flex-col justify-end gap-y-5 h-screen draggable'>
        {preview &&(
            <video autoPlay
        ref={videoElement}
        className={cn('w-6/12 seft-end bg-white')}
        ></video>)}
    <div className='rounded-full flex justify-around items-center h-20 w-full border-2 bg-[#171717] draggable border-white/40'>
    <div
        {...(onSources && {
            onClick: async () => {
                if (recording) {
                    StopRecording()
                    onStopRecording()
                } else {
                    try {
                        setRecording(true)
                        await StartRecording(onSources)
                    } catch (error) {
                        console.error('Failed to start recording:', error)
                        setRecording(false)
                    }
                }
            },
        })}
        className={cn(
            'non-draggable rounded-full relative hover:opacity-80 cursor-pointer',
            recording ? 'bg-red-500 w-6 h-6' : 'bg-red-400 w-8 h-8'
        )}
        >
        {recording && (
                <span className='absolute -right-16 top-1/2 transform -translate-y-1/2 text-white'>
                    {onTimer}
                </span>
        )}
    </div>
    {!recording ?
    (<Pause className='non-draggable opacity-50' size={32}
    fill='white'
    stroke='none'
     /> ): (
        <Square size={32} className='non-draggable cursor-pointer hover:scale-110 transform transition duration-150' fill='white'
        onClick={() => {
            setRecording(false)
            clearTime()
            StopRecording()
        }}
        stroke='white'
        />
    )}
    <Cast onClick={() => setPreview((prev) => !prev)}
    size={32}
    fill='white'
    className='non-draggable cursor-pointer hover:opacity-60'
    stroke='white'
    />
    </div>
    </div>
  )
  
}

export default StudioTray