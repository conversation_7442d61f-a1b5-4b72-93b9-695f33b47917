import { app, ipcMain, desktopCapturer, BrowserWindow } from "electron";
import { fileURLToPath } from "node:url";
import path from "node:path";
const __dirname = path.dirname(fileURLToPath(import.meta.url));
process.env.APP_ROOT = path.join(__dirname, "..");
const VITE_DEV_SERVER_URL = process.env["VITE_DEV_SERVER_URL"];
const MAIN_DIST = path.join(process.env.APP_ROOT, "dist-electron");
const RENDERER_DIST = path.join(process.env.APP_ROOT, "dist");
process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, "public") : RENDERER_DIST;
let win;
let studio;
let floatingWebcam;
function createWindow() {
  console.log("🏗️ Creating main window...");
  win = new BrowserWindow({
    width: 600,
    height: 600,
    minWidth: 300,
    minHeight: 600,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    focusable: false,
    show: false,
    icon: path.join(process.env.VITE_PUBLIC, "electron-vite.svg"),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      devTools: true,
      preload: path.join(__dirname, "preload.mjs")
    }
  });
  console.log("🎬 Creating studio window...");
  studio = new BrowserWindow({
    width: 400,
    height: 600,
    minWidth: 300,
    minHeight: 400,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    focusable: false,
    show: false,
    icon: path.join(process.env.VITE_PUBLIC, "electron-vite.svg"),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      devTools: true,
      preload: path.join(__dirname, "preload.mjs")
    }
  });
  console.log("📹 Creating webcam window...");
  floatingWebcam = new BrowserWindow({
    width: 400,
    height: 300,
    minWidth: 300,
    minHeight: 70,
    maxHeight: 400,
    maxWidth: 400,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    focusable: false,
    show: false,
    icon: path.join(process.env.VITE_PUBLIC, "electron-vite.svg"),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      devTools: true,
      preload: path.join(__dirname, "preload.mjs")
    }
  });
  console.log("⚙️ Setting window properties...");
  win.setVisibleOnAllWorkspaces(true, { visibleOnFullScreen: true });
  win.setAlwaysOnTop(true, "screen-saver", 1);
  studio.setVisibleOnAllWorkspaces(true, { visibleOnFullScreen: true });
  studio.setAlwaysOnTop(true, "screen-saver", 1);
  win.webContents.on("did-finish-load", () => {
    win == null ? void 0 : win.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  });
  studio.webContents.on("did-finish-load", () => {
    studio == null ? void 0 : studio.webContents.send(
      "main-process-message",
      (/* @__PURE__ */ new Date()).toLocaleString()
    );
  });
  console.log("🌐 Loading URLs in development mode:");
  console.log("Main window:", VITE_DEV_SERVER_URL);
  console.log("Studio window:", `${VITE_DEV_SERVER_URL}studio.html`);
  console.log("Webcam window:", `${VITE_DEV_SERVER_URL}webcam.html`);
  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL).catch((err) => {
      console.error("❌ Failed to load main window:", err);
    });
    studio.loadURL(`${VITE_DEV_SERVER_URL}studio.html`).catch((err) => {
      console.error("❌ Failed to load studio window:", err);
    });
    floatingWebcam.loadURL(`${VITE_DEV_SERVER_URL}webcam.html`).catch((err) => {
      console.error("❌ Failed to load webcam window:", err);
    });
  } else {
    console.log("📁 Loading files in production mode");
    win.loadFile(path.join(RENDERER_DIST, "index.html"));
    studio.loadFile(path.join(RENDERER_DIST, "studio.html"));
    floatingWebcam.loadFile(path.join(RENDERER_DIST, "webcam.html"));
  }
  win.webContents.on("did-fail-load", (_, errorCode, errorDescription, validatedURL) => {
    console.error("❌ Main window failed to load:", errorCode, errorDescription, validatedURL);
  });
  studio.webContents.on("did-fail-load", (_, errorCode, errorDescription, validatedURL) => {
    console.error("❌ Studio window failed to load:", errorCode, errorDescription, validatedURL);
  });
  win.webContents.on("did-finish-load", () => {
    console.log("✅ Main window loaded successfully");
    win == null ? void 0 : win.show();
  });
  studio.webContents.on("did-finish-load", () => {
    console.log("✅ Studio window loaded successfully");
    studio == null ? void 0 : studio.show();
  });
}
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
    win = null;
    studio = null;
    floatingWebcam = null;
  }
});
ipcMain.on("closeApp", () => {
  if (process.platform !== "darwin") {
    app.quit();
    win = null;
    studio = null;
    floatingWebcam = null;
  }
});
ipcMain.handle("getSources", async () => {
  return await desktopCapturer.getSources({
    thumbnailSize: { height: 100, width: 150 },
    fetchWindowIcons: true,
    types: ["window", "screen"]
  });
});
ipcMain.on("media-sources", (_, payload) => {
  studio == null ? void 0 : studio.webContents.send("profile-recieved", payload);
});
ipcMain.on("resize-studio", (_, payload) => {
  console.log("payload");
  if (payload.shrink) {
    studio == null ? void 0 : studio.setSize(400, 100);
  }
  if (!payload.shrink) {
    studio == null ? void 0 : studio.setSize(400, 250);
  }
});
ipcMain.on("hide-plugin", (_, payload) => {
  console.log("hide-plugin");
  win == null ? void 0 : win.webContents.send("hide-plugin", payload);
});
app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
console.log("🚀 Starting Electron app...", (/* @__PURE__ */ new Date()).toISOString());
console.log("📋 Electron main process loaded");
app.whenReady().then(() => {
  console.log("📱 App is ready, creating windows...");
  createWindow();
}).catch((err) => {
  console.error("❌ Failed to start app:", err);
});
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});
app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
export {
  MAIN_DIST,
  RENDERER_DIST,
  VITE_DEV_SERVER_URL
};
