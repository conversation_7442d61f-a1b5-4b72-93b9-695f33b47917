import { app, ipc<PERSON>ain, desktopCapturer, BrowserWindow } from "electron";
import { fileURLToPath } from "node:url";
import path from "node:path";
const __dirname = path.dirname(fileURLToPath(import.meta.url));
process.env.APP_ROOT = path.join(__dirname, "..");
const VITE_DEV_SERVER_URL = process.env["VITE_DEV_SERVER_URL"];
const MAIN_DIST = path.join(process.env.APP_ROOT, "dist-electron");
const RENDERER_DIST = path.join(process.env.APP_ROOT, "dist");
process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, "public") : RENDERER_DIST;
let win;
let studio;
let floatingWebcam;
function createWindow() {
  console.log("Creating main window...");
  win = new BrowserWindow({
    width: 400,
    height: 500,
    minWidth: 350,
    minHeight: 450,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    focusable: true,
    show: false,
    center: true,
    title: "LOMM Settings",
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      devTools: true,
      preload: path.join(__dirname, "preload.mjs")
    }
  });
  win.once("ready-to-show", () => {
    console.log("Main window ready to show");
    win == null ? void 0 : win.show();
    win == null ? void 0 : win.focus();
  });
  console.log("Creating studio window...");
  studio = new BrowserWindow({
    width: 300,
    height: 120,
    minWidth: 280,
    minHeight: 100,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    focusable: false,
    show: false,
    x: 100,
    y: 100,
    title: "LOMM Studio",
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      devTools: true,
      preload: path.join(__dirname, "preload.mjs")
    }
  });
  studio.once("ready-to-show", () => {
    console.log("Studio window ready to show");
    studio == null ? void 0 : studio.show();
    studio == null ? void 0 : studio.focus();
  });
  floatingWebcam = new BrowserWindow({
    width: 300,
    height: 200,
    minWidth: 250,
    minHeight: 150,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    focusable: false,
    show: false,
    x: 450,
    y: 100,
    title: "LOMM Webcam",
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      devTools: true,
      preload: path.join(__dirname, "preload.mjs")
    }
  });
  floatingWebcam.once("ready-to-show", () => {
    console.log("Webcam window ready to show");
    floatingWebcam == null ? void 0 : floatingWebcam.show();
  });
  win.webContents.on("did-finish-load", () => {
    win == null ? void 0 : win.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  });
  studio.webContents.on("did-finish-load", () => {
    studio == null ? void 0 : studio.webContents.send(
      "main-process-message",
      (/* @__PURE__ */ new Date()).toLocaleString()
    );
  });
  win.webContents.on("did-fail-load", (_, errorCode, errorDescription, validatedURL) => {
    console.error("Main window failed to load:", errorCode, errorDescription, validatedURL);
  });
  studio.webContents.on("did-fail-load", (_, errorCode, errorDescription, validatedURL) => {
    console.error("Studio window failed to load:", errorCode, errorDescription, validatedURL);
  });
  win.webContents.on("did-finish-load", () => {
    console.log("✅ Main window loaded successfully");
    win == null ? void 0 : win.show();
    win == null ? void 0 : win.focus();
  });
  studio.webContents.on("did-finish-load", () => {
    console.log("✅ Studio window loaded successfully");
    studio == null ? void 0 : studio.show();
    studio == null ? void 0 : studio.focus();
  });
  setTimeout(() => {
    console.log("🔍 Forcing windows to show...");
    if (win && !win.isDestroyed()) {
      win.show();
      win.focus();
      console.log("Main window visibility:", win.isVisible());
    }
    if (studio && !studio.isDestroyed()) {
      studio.show();
      studio.focus();
      console.log("Studio window visibility:", studio.isVisible());
    }
  }, 2e3);
  if (VITE_DEV_SERVER_URL) {
    console.log("Loading URLs in development mode:");
    console.log("Main window:", VITE_DEV_SERVER_URL);
    console.log("Studio window:", `${VITE_DEV_SERVER_URL}studio.html`);
    console.log("Webcam window:", `${VITE_DEV_SERVER_URL}webcam.html`);
    win.loadURL(VITE_DEV_SERVER_URL).catch((err) => {
      console.error("Failed to load main window:", err);
    });
    studio.loadURL(`${VITE_DEV_SERVER_URL}studio.html`).catch((err) => {
      console.error("Failed to load studio window:", err);
    });
    floatingWebcam.loadURL(`${VITE_DEV_SERVER_URL}webcam.html`).catch((err) => {
      console.error("Failed to load webcam window:", err);
    });
  } else {
    console.log("Loading files in production mode");
    win.loadFile(path.join(RENDERER_DIST, "index.html"));
    studio.loadFile(path.join(RENDERER_DIST, "studio.html"));
    floatingWebcam.loadFile(path.join(RENDERER_DIST, "webcam.html"));
  }
}
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
    win = null;
    studio = null;
    floatingWebcam = null;
  }
});
ipcMain.on("closeApp", () => {
  if (process.platform !== "darwin") {
    app.quit();
    win = null;
    studio = null;
    floatingWebcam = null;
  }
});
ipcMain.handle("getSources", async () => {
  return await desktopCapturer.getSources({
    thumbnailSize: { height: 100, width: 150 },
    fetchWindowIcons: true,
    types: ["window", "screen"]
  });
});
ipcMain.on("media-sources", (_, payload) => {
  studio == null ? void 0 : studio.webContents.send("profile-recieved", payload);
});
ipcMain.on("resize-studio", (_, payload) => {
  console.log("payload");
  if (payload.shrink) {
    studio == null ? void 0 : studio.setSize(400, 100);
  }
  if (!payload.shrink) {
    studio == null ? void 0 : studio.setSize(400, 250);
  }
});
ipcMain.on("hide-plugin", (_, payload) => {
  console.log("hide-plugin");
  win == null ? void 0 : win.webContents.send("hide-plugin", payload);
});
app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
console.log("🚀 Starting Electron app...");
app.whenReady().then(() => {
  console.log("📱 App is ready, creating windows...");
  createWindow();
}).catch((err) => {
  console.error("❌ Failed to start app:", err);
});
app.on("before-quit", () => {
  console.log("🛑 App is about to quit");
});
app.on("will-quit", () => {
  console.log("🛑 App will quit");
});
console.log("📋 Electron main process loaded");
export {
  MAIN_DIST,
  RENDERER_DIST,
  VITE_DEV_SERVER_URL
};
