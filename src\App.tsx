import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import './App.css'
import { Toaster } from 'sonner'
import ControlLayer from './layouts/ControlLayer'
import AuthButton from './components/Global/AuthButton'
import Widget from './components/Global/AuthButton/Widget'
import { useMediaSources } from './hooks/useMediaSources'
import MediaConfigurtion from './components/Global/MeidiaConfigration'
import { useEffect, useState } from 'react'

const client = new QueryClient()

// Development component that shows settings without authentication
const DevSettings = () => {
  const { state, fetchMediaResources } = useMediaSources()
  const [mockUser] = useState({
    id: 'dev-user',
    email: '<EMAIL>',
    firstName: 'Dev',
    lastName: 'User',
    createdAt: new Date().toISOString(),
    clerkId: 'user_2pqR8xK9L3mN4vB7wX2sY1tZ9',
    subscription: {
      plan: 'FREE' as const
    },
    studio: {
      id: 'dev-studio',
      screen: state.displays?.[0]?.id || null,
      mic: state.audioInputs?.[0]?.deviceId || null,
      preset: 'HD' as const,
      camera: null,
      userId: 'dev-user'
    }
  })

  useEffect(() => {
    fetchMediaResources()
  }, [])

  return (
    <div className='bg-[#171717] border-2 border-neutral-700 flex px-1 flex-col rounded-3xl overflow-hidden h-screen draggable'>
      <div className='flex justify-between items-center p-5 draggable'>
        <span className='text-white'>
          Dev User
        </span>
        <div className='text-gray-400 non-draggable hover:text-white cursor-pointer'>
          ✕
        </div>
      </div>
      <div className='flex-1 h-0 overflow-auto'>
        {state.isPending ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-4"></div>
            <p className="text-sm text-gray-400">Loading media sources...</p>
          </div>
        ) : state.error ? (
          <div className="text-center py-4">
            <div className="text-red-400 mb-4">
              <p className="text-sm">⚠️ {state.error}</p>
            </div>
            <button
              onClick={fetchMediaResources}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm transition-colors non-draggable"
            >
              Retry
            </button>
          </div>
        ) : (
          <MediaConfigurtion state={state} user={mockUser} />
        )}
      </div>
      <div className='p-5 flex w-full'>
        <div className='flex items-center gap-x-2'>
          <img src="" alt="lomm" />
          <p className='text-white text-2xl'>Lomm</p>
        </div>
      </div>
    </div>
  )
}

function App() {
  // For development, show settings directly to avoid authentication issues
  const isDev = import.meta.env.DEV

  if (isDev) {
    return (
      <QueryClientProvider client={client}>
        <DevSettings />
        <Toaster />
      </QueryClientProvider>
    )
  }

  // Production version with authentication
  return (
    <QueryClientProvider client={client}>
      <ControlLayer>
        <AuthButton/>
        <Widget/>
      </ControlLayer>
      <Toaster/>
    </QueryClientProvider>
  )
}

export default App
