// import reactLogo from './assets/react.svg'
// import viteLogo from '/electron-vite.animate.svg'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import './App.css'
import { Toaster } from 'sonner'
import ControlLayer from './layouts/ControlLayer'
import AuthButton from './components/Global/AuthButton'
import Widget from './components/Global/AuthButton/Widget'
import { useMediaSources } from './hooks/useMediaSources'
import MediaConfigurtion from './components/Global/MeidiaConfigration'
import { useEffect, useState } from 'react'

const client = new QueryClient()

// Development component that shows settings without authentication
const DevSettings = () => {
  const { state, fetchMediaResources } = useMediaSources()
  const [mockUser] = useState({
    id: 'dev-user',
    email: '<EMAIL>',
    firstName: 'Dev',
    lastName: 'User',
    createdAt: new Date().toISOString(),
    clerkId: 'dev-user',
    subscription: {
      plan: 'FREE' as const
    },
    studio: {
      id: 'dev-studio',
      screen: null,
      mic: null,
      preset: 'HD' as const,
      camera: null,
      userId: 'dev-user'
    }
  })

  useEffect(() => {
    fetchMediaResources()
  }, [])

  return (
    <div className="p-5 bg-[#1a1a1a] min-h-screen text-white">
      <div className="max-w-md mx-auto">
        <h1 className="text-2xl font-bold mb-6 text-center">LOMM Settings</h1>
        {state.isPending ? (
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
            <p>Loading media sources...</p>
          </div>
        ) : state.error ? (
          <div className="text-center text-red-400">
            <p>Error: {state.error}</p>
            <button
              onClick={fetchMediaResources}
              className="mt-4 px-4 py-2 bg-blue-600 rounded hover:bg-blue-700"
            >
              Retry
            </button>
          </div>
        ) : (
          <MediaConfigurtion state={state} user={mockUser} />
        )}
      </div>
    </div>
  )
}

function App() {
  // For development, show settings directly
  // In production, you'd use the authentication flow
  const isDev = import.meta.env.DEV

  if (isDev) {
    return (
      <QueryClientProvider client={client}>
        <DevSettings />
        <Toaster />
      </QueryClientProvider>
    )
  }

  return (
    <QueryClientProvider client={client}>
      <ControlLayer>
        <AuthButton/>
        <Widget/>
      </ControlLayer>
      <Toaster/>
    </QueryClientProvider>
  )
}

export default App
