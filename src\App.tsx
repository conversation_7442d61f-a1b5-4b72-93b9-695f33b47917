// import reactLogo from './assets/react.svg'
// import viteLogo from '/electron-vite.animate.svg'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import './App.css'
import { Toaster } from 'sonner'
import ControlLayer from './layouts/ControlLayer'
import AuthButton from './components/Global/AuthButton'
import Widget from './components/Global/AuthButton/Widget'
import { useMediaSources } from './hooks/useMediaSources'
import MediaConfigurtion from './components/Global/MeidiaConfigration'
import { useEffect, useState } from 'react'

const client = new QueryClient()

// Development component that shows settings without authentication
const DevSettings = () => {
  const { state, fetchMediaResources } = useMediaSources()
  const [mockUser] = useState({
    id: 'dev-user',
    email: '<EMAIL>',
    firstName: 'Dev',
    lastName: 'User',
    createdAt: new Date().toISOString(),
    clerkId: 'dev-user',
    subscription: {
      plan: 'FREE' as const
    },
    studio: {
      id: 'dev-studio',
      screen: null,
      mic: null,
      preset: 'HD' as const,
      camera: null,
      userId: 'dev-user'
    }
  })

  useEffect(() => {
    fetchMediaResources()
  }, [])

  return (
    <div className="min-h-screen text-white p-4 draggable">
      <div className="bg-black/80 backdrop-blur-md rounded-2xl border border-white/20 shadow-2xl p-6 max-w-sm mx-auto">
        {/* Header with Logo and Title */}
        <div className="text-center mb-6">
          <div className="flex items-center justify-center gap-2 mb-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">L</span>
            </div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              LOMM
            </h1>
          </div>
          <p className="text-xs text-gray-400">Screen Recording Studio</p>
        </div>

        {/* User Info */}
        <div className="mb-4 p-3 bg-white/5 rounded-lg border border-white/10">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xs">✓</span>
            </div>
            <div>
              <p className="text-sm font-medium">{mockUser.firstName} {mockUser.lastName}</p>
              <p className="text-xs text-gray-400 font-mono">{mockUser.clerkId.slice(0, 12)}...</p>
            </div>
          </div>
        </div>

        {/* Settings Content */}
        {state.isPending ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-4"></div>
            <p className="text-sm text-gray-400">Loading media sources...</p>
          </div>
        ) : state.error ? (
          <div className="text-center py-4">
            <div className="text-red-400 mb-4">
              <p className="text-sm">⚠️ {state.error}</p>
            </div>
            <button
              onClick={fetchMediaResources}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm transition-colors non-draggable"
            >
              Retry
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            <MediaConfigurtion state={state} user={mockUser} />
          </div>
        )}

        {/* Footer */}
        <div className="mt-6 pt-4 border-t border-white/10">
          <div className="flex justify-between items-center text-xs text-gray-500">
            <span>{mockUser.subscription.plan} Plan</span>
            <span>v1.0.0</span>
          </div>
        </div>
      </div>
    </div>
  )
}

function App() {
  // For development, show settings directly
  // In production, you'd use the authentication flow
  const isDev = import.meta.env.DEV

  if (isDev) {
    return (
      <QueryClientProvider client={client}>
        <DevSettings />
        <Toaster />
      </QueryClientProvider>
    )
  }

  return (
    <QueryClientProvider client={client}>
      <ControlLayer>
        <AuthButton/>
        <Widget/>
      </ControlLayer>
      <Toaster/>
    </QueryClientProvider>
  )
}

export default App
