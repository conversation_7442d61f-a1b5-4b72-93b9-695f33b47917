import { hidePluginWindow } from "./utils"
import { v4 as uuid } from "uuid"

let videoTransferFileName: string | undefined
let mediaRecorder: MediaRecorder | null = null
let mediaStream: MediaStream | null = null

export const StartRecording = async (onSources: {
    screen: string
    audio: string
    id: string
    preset?: 'HD' | 'SD'
}) => {
    try {
        hidePluginWindow(true)

        // Generate filename with proper extension
        videoTransferFileName = `${uuid()}-${onSources?.id.slice(0, 8)}.webm`

        // Validate inputs
        if (!onSources.screen) {
            throw new Error('No screen source selected')
        }
        if (!onSources.audio) {
            throw new Error('No audio source selected')
        }

        console.log('Starting recording with sources:', {
            screen: onSources.screen,
            audio: onSources.audio,
            preset: onSources.preset
        })

        // Get screen capture stream
        let stream: MediaStream;
        try {
            stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    // @ts-ignore - Electron specific API
                    mandatory: {
                        chromeMediaSource: 'desktop',
                        chromeMediaSourceId: onSources.screen,
                        minWidth: onSources.preset === 'HD' ? 1920 : 1280,
                        maxWidth: onSources.preset === 'HD' ? 1920 : 1280,
                        minHeight: onSources.preset === 'HD' ? 1080 : 720,
                        maxHeight: onSources.preset === 'HD' ? 1080 : 720
                    }
                },
                audio: {
                    deviceId: { exact: onSources.audio }
                }
            })
        } catch (error) {
            console.warn('Failed with exact audio device, trying with ideal:', error);
            // Fallback: try with ideal instead of exact
            stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    // @ts-ignore - Electron specific API
                    mandatory: {
                        chromeMediaSource: 'desktop',
                        chromeMediaSourceId: onSources.screen,
                        minWidth: onSources.preset === 'HD' ? 1920 : 1280,
                        maxWidth: onSources.preset === 'HD' ? 1920 : 1280,
                        minHeight: onSources.preset === 'HD' ? 1080 : 720,
                        maxHeight: onSources.preset === 'HD' ? 1080 : 720
                    }
                },
                audio: {
                    deviceId: onSources.audio
                }
            })
        }

        mediaStream = stream

        // Initialize MediaRecorder
        mediaRecorder = new MediaRecorder(stream, {
            mimeType: 'video/webm;codecs=vp9'
        })

        const chunks: Blob[] = []

        mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
                chunks.push(event.data)
            }
        }

        mediaRecorder.onstop = () => {
            const blob = new Blob(chunks, { type: 'video/webm' })
            // Handle the recorded video blob here
            console.log('Recording stopped, blob size:', blob.size)

            // You can save the blob or send it to your backend
            saveRecording(blob, videoTransferFileName!)
        }

        mediaRecorder.onerror = (event) => {
            console.error('MediaRecorder error:', event)
        }

        // Start recording with 1 second intervals
        mediaRecorder.start(1000)
        console.log('Recording started with filename:', videoTransferFileName)

    } catch (error) {
        console.error('Error starting recording:', error)
        throw error
    }
}

export const StopRecording = () => {
    if (mediaRecorder && mediaRecorder.state === 'recording') {
        mediaRecorder.stop()
    }

    if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop())
        mediaStream = null
    }

    hidePluginWindow(false)
    console.log('Recording stopped')
}

const saveRecording = async (blob: Blob, filename: string) => {
    try {
        // Create a download link for the recorded video
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = filename
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)

        console.log('Recording saved:', filename)
    } catch (error) {
        console.error('Error saving recording:', error)
    }
}

export const getRecordingState = () => {
    return {
        isRecording: mediaRecorder?.state === 'recording',
        filename: videoTransferFileName,
        state: mediaRecorder?.state || 'inactive'
    }
}

export const pauseRecording = () => {
    if (mediaRecorder && mediaRecorder.state === 'recording') {
        mediaRecorder.pause()
        console.log('Recording paused')
    }
}

export const resumeRecording = () => {
    if (mediaRecorder && mediaRecorder.state === 'paused') {
        mediaRecorder.resume()
        console.log('Recording resumed')
    }
}

export const selectSources = asynn (

) 