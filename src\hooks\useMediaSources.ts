import { getMediaSources } from "@/lib/utils";
import { useReducer } from "react";

export type SourceDeviceStateProps = {
  displays?: {
    appIcon: null;
    id: string;
    name: string;
    thumnail: unknown[];
  }[];
  audioInputs?: {
    deviceId: string;
    groupId: string;
    kind: string;
    label: string;
  }[];
  error?: string | null;
  isPending: boolean;
};

type DisplayDeviceActionProps = {
    type: 'GET_DEVICES';
    payload?: SourceDeviceStateProps
}

export const useMediaSources = () => {
  const [state, dispatch] = useReducer((state: SourceDeviceStateProps, action: DisplayDeviceActionProps) => {
    switch (action.type) {
      case "GET_DEVICES":
        return {
          ...state,
          ...action.payload,
        };
      default:
        return state;
    }
  }, {
    displays: [],
    audioInputs: [],
    error: null,
    isPending: false,
  });

  const fetchMediaResources = () => {
    dispatch({ type: 'GET_DEVICES', payload: { isPending: true } });
    getMediaSources()
      .then((sources) =>
        dispatch({
          type: 'GET_DEVICES',
          payload: {
            displays: sources.displays,
            audioInputs: sources.audio,
            isPending: false,
            error: null,
          }
        })
      )
      .catch((error) => {
        console.error('Failed to fetch media resources:', error);
        dispatch({
          type: 'GET_DEVICES',
          payload: {
            displays: [],
            audioInputs: [],
            isPending: false,
            error: 'Failed to get media devices. Please check permissions.',
          }
        });
      });
  };
  return { state, fetchMediaResources };
};
